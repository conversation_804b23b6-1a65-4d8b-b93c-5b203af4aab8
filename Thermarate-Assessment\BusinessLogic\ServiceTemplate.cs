using RediSoftware.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using RediSoftware.Dtos;
using AutoMapper;
using RediSoftware.Redi_Utility;
using RediSoftware.Helpers;
using RediSoftware.App_Start;
using RediSoftware.Common;
using System.Globalization;
using AutoMapper.QueryableExtensions;
using Kendo.DynamicLinq;
using System.Configuration;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace RediSoftware.BusinessLogic
{
    /// <summary>
    /// Provides methods to update both SURFACES and OPENINGS.
    /// </summary>
    public class ServiceTemplate : BusinessLogicBase
    {
        public ServiceTemplate(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _mapper = mapper;
            _unitOfWork = unitOfWork;
        }

        private IQueryable<RSS_ServiceTemplateView> InnerGet()
        {
            _unitOfWork.ReadOnly();

            IQueryable<RSS_ServiceTemplateView> query = _unitOfWork.Context.RSS_ServiceTemplateView.Where(a => a.Deleted == false);

            return query;
        }

        // Adjust query given a set of multi-select filters - following ConstructionController pattern exactly
        private IQueryable<RSS_ServiceTemplateView> AlterQueryFromMultiFiltersForView(IQueryable<RSS_ServiceTemplateView> query, ServiceFilterDataDto filterData)
        {
            if (filterData.appliedFilters == null)
                return query;

            foreach (var filterField in filterData.appliedFilters.Properties())
            {
                var fieldName = filterField.Name;
                var filterValues = filterField.Value.ToObject<List<string>>();

                // Skip if no values or if "Any" is selected (following ConstructionController pattern)
                if (filterValues == null || !filterValues.Any() ||
                    filterValues.Any(x => x?.ToLower() == "any"))
                    continue;

                switch (fieldName)
                {
                    case "ServiceCategoryTitle":
                        if (filterValues.Any(x => x?.ToLower() == "not specified"))
                        {
                            var nonNullValues = filterValues.Where(x => x?.ToLower() != "not specified").ToList();
                            if (nonNullValues.Any())
                            {
                                query = query.Where(x => nonNullValues.Contains(x.ServiceCategoryTitle) || string.IsNullOrEmpty(x.ServiceCategoryTitle));
                            }
                            else
                            {
                                query = query.Where(x => string.IsNullOrEmpty(x.ServiceCategoryTitle));
                            }
                        }
                        else
                        {
                            query = query.Where(x => filterValues.Contains(x.ServiceCategoryTitle));
                        }
                        break;
                    case "ManufacturerDescription":
                        if (filterValues.Any(x => x?.ToLower() == "not specified"))
                        {
                            var nonNullValues = filterValues.Where(x => x?.ToLower() != "not specified").ToList();
                            if (nonNullValues.Any())
                            {
                                query = query.Where(x => nonNullValues.Contains(x.ManufacturerDescription) || string.IsNullOrEmpty(x.ManufacturerDescription));
                            }
                            else
                            {
                                query = query.Where(x => string.IsNullOrEmpty(x.ManufacturerDescription));
                            }
                        }
                        else
                        {
                            query = query.Where(x => filterValues.Contains(x.ManufacturerDescription));
                        }
                        break;
                }
            }

            return query;
        }

        public ServiceTemplateDto Get(Guid ServiceTemplateId)
        {
            _unitOfWork.ReadOnly();

            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(ServiceTemplateId);
            var dto = _mapper.Map<ServiceTemplateDto>(dbModel);
            return dto;
        }

        /// <summary>
        /// Returns a summarized view of our Service Templates.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public DataSourceResult<RSS_ServiceTemplateView> GetList(
            string fromDate = null,
            string toDate = null,
            bool? isDeleted = false,
            PagingParameters paging = null)
        {
            DateTime dtFromDate = DateTime.MinValue;
            DateTime dtToDate = DateTime.MaxValue;
            if (fromDate != null)
                dtFromDate = DateTime.Parse(fromDate, null, DateTimeStyles.RoundtripKind).ToLocalTime();
            if (toDate != null)
                dtToDate = DateTime.Parse(toDate, null, DateTimeStyles.RoundtripKind).ToLocalTime();

            paging.SetDefaultSort("Description");
            _unitOfWork.ReadOnly();

            var templates = _unitOfWork.Context.RSS_ServiceTemplateView
                    .Where(a => (a.CreatedOn >= dtFromDate && a.CreatedOn <= dtToDate) && (a.Deleted == isDeleted))
                    .ToDataSourceResult(paging.PageSize, paging.Skip, paging.Sort, paging.Filter, paging.Aggregate, paging.Export);

            return templates;
        }

        public List<ServiceTemplateDto> GetAll()
        {
            return _unitOfWork.Context.RSS_ServiceTemplate
                .Where(x => x.Deleted == false)
                .OrderBy(x => x.Description)
                .ProjectTo<ServiceTemplateDto>(_mapper.ConfigurationProvider)
                .ToList();
        }

        public Guid Create(ServiceTemplateDto serviceTemplateDto)
        {
            serviceTemplateDto.CreatedOn = DateTime.Now;
            serviceTemplateDto.CreatedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            serviceTemplateDto.ServiceTemplateId = Guid.NewGuid();
            RSS_ServiceTemplate _dbModel = _mapper.Map<RSS_ServiceTemplate>(serviceTemplateDto);

            _unitOfWork.Context.RSS_ServiceTemplate.Add(_dbModel);
            _unitOfWork.Commit();

            // Clear cache when Service Database records are created
            ClearServiceFilterCache();

            this.RecordAdminEvent(AdminChangeType.Add, serviceTemplateDto.Description, "Added new ServiceTemplate.");
            _unitOfWork.Commit();
            return _dbModel.ServiceTemplateId;
        }

        public Guid Update(ServiceTemplateDto serviceTemplateDto)
        {
            serviceTemplateDto.ModifiedOn = DateTime.Now;
            serviceTemplateDto.ModifiedByName = UtilityFunctions.CurrentUserName ?? "unknown";

            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(serviceTemplateDto.ServiceTemplateId);

            _mapper.Map(serviceTemplateDto, dbModel);
            _unitOfWork.Commit();

            // Clear cache when Service Database records are updated
            ClearServiceFilterCache();

            this.RecordAdminEvent(AdminChangeType.Update, serviceTemplateDto.Description, "Updated ServiceTemplate.");
            return dbModel.ServiceTemplateId;
        }

        public void Delete(Guid id)
        {
            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(id);
            dbModel.ModifiedOn = DateTime.Now;
            dbModel.ModifiedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            dbModel.Deleted = true;
            this.RecordAdminEvent(AdminChangeType.Delete, dbModel.Description, "");
            _unitOfWork.Commit();

            // Clear cache when Service Database records are deleted
            ClearServiceFilterCache();
        }

        public void UndoDelete(Guid id)
        {

            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(id);
            dbModel.ModifiedOn = DateTime.Now;
            dbModel.ModifiedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            dbModel.Deleted = false;
            this.RecordAdminEvent(AdminChangeType.Delete, dbModel.Description, "");
            _unitOfWork.Commit();

            // Clear cache when Service Database records are undeleted
            ClearServiceFilterCache();
        }

        public Guid Copy(Guid id)
        {
            var model = Get(id);
            model.ServiceTemplateId = Guid.NewGuid();
            model.CreatedOn = DateTime.Now;
            model.CreatedByName = UtilityFunctions.CurrentUserName ?? "unknown";
            model.ModifiedOn = null;
            model.ModifiedByName = null;
            model.Deleted = false;
            model.Description = model.Description + " Copy";

            var mapped = _mapper.Map<RSS_ServiceTemplate>(model);
            _unitOfWork.Context.RSS_ServiceTemplate.Add(mapped);

            this.RecordAdminEvent(AdminChangeType.Delete, model.Description, "");
            _unitOfWork.Commit();

            // Clear cache when Service Database records are copied
            ClearServiceFilterCache();

            return model.ServiceTemplateId;
        }


        public List<ServiceCategoryDto> GetServiceCategories()
        {
            var list = _unitOfWork.Context.RSS_ServiceCategory
                .Where(a => a.Deleted == false)
                .OrderBy(a => a.SortOrder)
                .ProjectTo<ServiceCategoryDto>(_mapper.ConfigurationProvider)
                .ToList();

            return list;
        }


        public static Dictionary<string, bool> AssignDefaultColumnVisibility(string serviceCategoryCode)
        {
            // Used for intial column show/hide state.
            // Properties not in this list will be shown.

            var defaultHiddenColumns = new Dictionary<string, bool>();

            // Currently no properties are hidden by default for services. Leaving here as a stub.
            // Refer to Construction.AssignDefaultColumnVisibility for an example.

            return defaultHiddenColumns;
        }

        public void SetIsFavourite(Guid serviceTemplateId, bool isFavourite)
        {
            var dbModel = _unitOfWork.Context.RSS_ServiceTemplate.Find(serviceTemplateId);

            if (dbModel == null)
            {
                throw new Exception(string.Format("{1} row not found for Id: {0}", serviceTemplateId, "RSS_ServiceTemplate"));
            }

            // Update the database model
            dbModel.ModifiedOn = DateTime.Now;
            dbModel.ModifiedByName = UtilityFunctions.CurrentUserFullName ?? "unknown";
            dbModel.IsFavourite = isFavourite;

            _unitOfWork.Commit();

            // Clear cache when Service Database records are updated
            ClearServiceFilterCache();
        }

        public DataSourceResult<RSS_ServiceTemplateView> GetMultiFiltered(ServiceFilterDataDto filterData)
        {
            // Debug logging for service filters
            if (filterData.appliedFilters != null)
            {
                foreach (var filter in filterData.appliedFilters.Properties())
                {
                    var filterValues = filter.Value.ToObject<List<string>>();
                    System.Diagnostics.Debug.WriteLine($"Service filter {filter.Name}: {string.Join(", ", filterValues)}");
                }
            }

            // Use the service template view
            var query = _unitOfWork.Context.RSS_ServiceTemplateView.Where(x => !x.Deleted);

            // Debug: Count records before applying filters
            var countBeforeFilters = query.Count();
            System.Diagnostics.Debug.WriteLine($"Service records before applying filters: {countBeforeFilters}");

            if (filterData.appliedFilters != null)
                query = AlterQueryFromMultiFiltersForView(query, filterData);

            // Debug: Count records after applying filters
            var countAfterFilters = query.Count();
            System.Diagnostics.Debug.WriteLine($"Service records after applying filters: {countAfterFilters}");

            var results = query
                .ToDataSourceResult(filterData.paging.PageSize, filterData.paging.Skip, filterData.paging?.Sort ?? new List<string>() { "CreatedOn@@desc" }, filterData.paging?.Filter, filterData.paging?.Aggregate, filterData.paging?.Export);

            return results;
        }

        public Dictionary<string, List<FilterOption>> GetMultiFilterOptions(ServiceFilterOptionsDto request)
        {
            var filterOptions = new Dictionary<string, List<FilterOption>>();

            // Add null checking
            if (request == null)
                throw new ArgumentNullException(nameof(request), "Request cannot be null");

            if (request.fieldsList == null)
                throw new ArgumentNullException(nameof(request.fieldsList), "FieldsList cannot be null");

            // Use the service template view
            var query = _unitOfWork.Context.RSS_ServiceTemplateView.Where(x => !x.Deleted);

            // Get Service Category options
            var categoryOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };
            var categoryData = query
                .Where(x => !string.IsNullOrEmpty(x.ServiceCategoryTitle))
                .GroupBy(x => x.ServiceCategoryTitle)
                .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                .OrderBy(x => x.name)
                .ToList();
            categoryOptions.AddRange(categoryData);
            filterOptions["ServiceCategoryTitle"] = categoryOptions;

            // Add Manufacturer filter options
            var manufacturerOptions = new List<FilterOption> { new FilterOption { name = "Any", value = "Any" } };

            // Check if there are any null or empty values first
            var hasNullOrEmptyManufacturer = query
                .Any(x => string.IsNullOrEmpty(x.ManufacturerDescription));

            // Add "Not Specified" at the top (after "Any") if there are null/empty values
            if (hasNullOrEmptyManufacturer)
            {
                manufacturerOptions.Add(new FilterOption { name = "Not Specified", value = "Not Specified" });
            }

            var manufacturerData = query
                .Where(x => !string.IsNullOrEmpty(x.ManufacturerDescription))
                .GroupBy(x => x.ManufacturerDescription)
                .Select(g => new FilterOption { name = g.Key, value = g.Key, count = g.Count() })
                .OrderBy(x => x.name)
                .ToList();
            manufacturerOptions.AddRange(manufacturerData);
            filterOptions["ManufacturerDescription"] = manufacturerOptions;

            return filterOptions;
        }

        public async Task<Dictionary<string, Dictionary<string, int>>> GetFilterCountData(ServiceFilterDataDto filterData)
        {
            // Current filters applied
            bool anyFiltersApplied = false;
            Dictionary<string, List<string>> currentFilters = new Dictionary<string, List<string>>();

            if (filterData.fields != null && filterData.appliedFilters != null)
            {
                foreach (MultiFilterFieldDto field in filterData.fields.Where(f => !f.isDate))
                {
                    if (filterData.appliedFilters.ContainsKey(field.field) && filterData.appliedFilters[field.field] != null)
                    {
                        List<string> fieldSelections = JsonConvert.DeserializeObject<List<string>>(filterData.appliedFilters[field.field].ToString());
                        bool hasFilter = fieldSelections != null && fieldSelections.Any() && !fieldSelections.Contains("Any");
                        currentFilters[field.field] = hasFilter ? fieldSelections : null;
                        if (!anyFiltersApplied && hasFilter) { anyFiltersApplied = true; }
                    }
                }
            }

            // Generate cache key based on filters
            string cacheKey = GenerateServiceFilterCacheKey(filterData, anyFiltersApplied);

            // Check if we should bypass cache (for admin users)
            bool bypasseCache = SystemParameter.GetIntParm("BypassFilterCountDataCacheAdmin") == 1;

            // Try to get from cache if not bypassing
            if (!bypasseCache)
            {
                // Try to get from the specific cache key first
                var cachedData = (Dictionary<string, Dictionary<string, int>>)CacheHandler.LookupCache(
                    CacheHandler.DataType_MultiFilterCountData, cacheKey);

                if (cachedData != null)
                {
                    return cachedData;
                }

                // If no filters are applied, try the general cache
                if (!anyFiltersApplied)
                {
                    string generalCacheKey = "ServiceFilterCountData";
                    cachedData = (Dictionary<string, Dictionary<string, int>>)CacheHandler.LookupCache(
                        CacheHandler.DataType_MultiFilterCountData, generalCacheKey);

                    if (cachedData != null)
                    {
                        return cachedData;
                    }
                }
            }

            // Initialize filter count data just before database connection - following ConstructionController pattern
            Dictionary<string, Dictionary<string, int>> filterCountData = new Dictionary<string, Dictionary<string, int>>();

            using (var connection = new SqlConnection(_unitOfWork.Context.Database.Connection.ConnectionString))
            {
                await connection.OpenAsync();

                try
                {
                    // Build sql condition for current filters
                    List<FieldSqlCondition> currentFiltersSqlConditions = GetServiceSqlConditions();

                    // FOR EACH field
                    foreach (MultiFilterFieldDto field in filterData.fields)
                    {
                        string fieldName = field.field;
                        string fieldCondition = null;

                        // Only process if the field exists in appliedFilters
                        if (filterData.appliedFilters != null && filterData.appliedFilters.ContainsKey(field.field) && filterData.appliedFilters[field.field] != null)
                        {
                            string fieldAppliedFilters = filterData.appliedFilters[field.field].ToString();

                            // Normal field (no date fields for service templates)
                            if (currentFilters.ContainsKey(fieldName) && currentFilters[fieldName] != null && currentFilters[fieldName]?.Where(s => s.ToString()?.ToLower() != "not specified").Count() > 0)
                            {
                                fieldCondition = $"[{fieldName}] IN ({string.Join(",", currentFilters[fieldName].Where(s => s.ToString()?.ToLower() != "not specified").Select(s => field.isDecimal ? $"{s}" : $"'{s}'"))})";
                                if (currentFilters[fieldName].Select(s => s.ToLower()).Contains("not specified"))
                                {
                                    fieldCondition = $"({fieldCondition} OR [{fieldName}] IS NULL)";
                                }
                            }
                            else if (currentFilters.ContainsKey(fieldName) && currentFilters[fieldName]?.Where(s => s.ToString()?.ToLower() == "not specified").Count() > 0)
                            {
                                fieldCondition = $"[{fieldName}] IS NULL";
                            }

                            // Add to current filters list for sql
                            if (!string.IsNullOrEmpty(fieldCondition))
                            {
                                currentFiltersSqlConditions.Add(new FieldSqlCondition { fieldName = fieldName, sql = fieldCondition });
                            }
                        }
                    }

                    // Build SQL query using the same pattern as ConstructionController
                    string sql = BuildServiceFilterCountSqlAdvanced(filterData, currentFiltersSqlConditions);

                    if (string.IsNullOrEmpty(sql))
                    {
                        return new Dictionary<string, Dictionary<string, int>>();
                    }

                    var cmd = new SqlCommand(sql, connection);

                    using (var sqlReader = await cmd.ExecuteReaderAsync())
                    {
                        // Read each option in each field
                        while (sqlReader.Read())
                        {
                            string fieldName = (string)sqlReader["FieldName"];
                            string optionName = (string)sqlReader["OptionName"];
                            int count = (int)sqlReader["Count"];
                            Dictionary<string, int> fieldCountData = null;
                            if (filterCountData.TryGetValue(fieldName, out fieldCountData))
                                fieldCountData[optionName] = count;
                            else
                                filterCountData[fieldName] = new Dictionary<string, int> { { optionName, count } };
                        }
                        sqlReader.Close();
                    }

                    // Always save to filter-specific cache
                    CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterCountData, cacheKey, filterCountData);

                    // If no filters applied, also save to the general cache
                    if (!anyFiltersApplied)
                    {
                        string generalCacheKey = "ServiceFilterCountData";
                        CacheHandler.SaveToCache(CacheHandler.DataType_MultiFilterCountData, generalCacheKey, filterCountData);
                    }

                    // Make sure next call uses cache
                    if (bypasseCache)
                    {
                        SystemParameter.UpdateIntParm("BypassFilterCountDataCacheAdmin", 0);
                    }

                    return filterCountData;
                }
                catch (Exception e)
                {
                    throw e;
                }
            }
        }

        // Helper method to generate a cache key based on the applied filters
        private string GenerateServiceFilterCacheKey(ServiceFilterDataDto filterData, bool anyFiltersApplied)
        {
            string baseKey = "ServiceFilterCountData";

            // If no filters are applied, return the base key
            if (!anyFiltersApplied || filterData.appliedFilters == null)
                return baseKey;

            // Create a hash of the applied filters
            var filterHash = JsonConvert.SerializeObject(filterData.appliedFilters).GetHashCode().ToString("X");
            return $"{baseKey}_{filterHash}";
        }

        // Helper class for SQL conditions - following ConstructionController pattern
        private class FieldSqlCondition
        {
            public string fieldName { get; set; }
            public string sql { get; set; }
        }

        // Helper method to get column name for filter field - following ConstructionController pattern
        private string GetServiceColumnNameForField(string fieldName)
        {
            switch (fieldName)
            {
                case "ServiceCategoryTitle":
                    return "ServiceCategoryTitle";
                case "ManufacturerDescription":
                    return "ManufacturerDescription";
                default:
                    return null;
            }
        }

        // Helper method to get base SQL conditions for services - following ConstructionController pattern
        private List<FieldSqlCondition> GetServiceSqlConditions()
        {
            var conditions = new List<FieldSqlCondition>();

            // Add base condition for non-deleted records
            conditions.Add(new FieldSqlCondition
            {
                fieldName = "BaseCondition",
                sql = "v.Deleted = 0"
            });

            return conditions;
        }

        // Advanced SQL builder that matches ConstructionController pattern exactly
        private string BuildServiceFilterCountSqlAdvanced(ServiceFilterDataDto filterData, List<FieldSqlCondition> currentFiltersSqlConditions)
        {
            if (filterData.fields == null || !filterData.fields.Any())
                return "";

            // Build SQL using UNION pattern exactly like ConstructionController
            var sqlParts = new List<string>();

            foreach (var field in filterData.fields)
            {
                var fieldName = field.field;
                var columnName = GetServiceColumnNameForField(fieldName);

                if (string.IsNullOrEmpty(columnName))
                    continue;

                // Get conditions excluding this field, but include base conditions (deleted, etc.)
                var conditions = currentFiltersSqlConditions
                    .Where(c => c.fieldName != fieldName)
                    .Select(c => c.sql)
                    .ToList();

                // Always include base conditions for deleted records
                var baseConditions = GetServiceSqlConditions();
                foreach (var baseCondition in baseConditions)
                {
                    if (!conditions.Any(c => c.Contains(baseCondition.sql)))
                    {
                        conditions.Add(baseCondition.sql);
                    }
                }

                var whereClause = conditions.Any() ? string.Join(" AND ", conditions) : "1=1";

                // Get all distinct values from database for this field (following ConstructionController pattern)
                sqlParts.Add($@"
                    SELECT '{fieldName}' [FieldName]
                           ,v.[{columnName}] [OptionName]
                           ,COUNT(*) [Count]
                    FROM [dbo].[RSS_ServiceTemplateView] v
                    WHERE {whereClause} AND v.[{columnName}] IS NOT NULL AND v.[{columnName}] != ''
                    GROUP BY v.[{columnName}]");

                // Add "Any" count
                sqlParts.Add($@"
                    SELECT '{fieldName}' [FieldName]
                           ,'Any' [OptionName]
                           ,COUNT(*) [Count]
                    FROM [dbo].[RSS_ServiceTemplateView] v
                    WHERE {whereClause}");

                // Add "Not Specified" count
                sqlParts.Add($@"
                    SELECT '{fieldName}' [FieldName]
                           ,'Not Specified' [OptionName]
                           ,COUNT(*) [Count]
                    FROM [dbo].[RSS_ServiceTemplateView] v
                    WHERE {whereClause} AND (v.[{columnName}] IS NULL OR v.[{columnName}] = '')");
            }

            return string.Join(" UNION ", sqlParts);
        }

        // Helper method to clear Service Database caches when data is updated - following ConstructionController pattern
        private void ClearServiceFilterCache()
        {
            // Clear filter count data cache
            CacheHandler.DeleteCacheLikeItems(CacheHandler.DataType_MultiFilterCountData, "ServiceFilterCountData");

            // Clear multi-filter options cache
            CacheHandler.DeleteCacheLikeItems(CacheHandler.DataType_MultiFilterOptions, "ServiceMultiFilterOptions_");

            // Set bypass cache parameters to force fresh data on next retrieval
            SystemParameter.UpdateIntParm("BypassFilterCountDataCacheAdmin", 1);
            SystemParameter.UpdateIntParm("BypassMultiFilterOptionsCacheAdmin", 1);
        }

    }
}
